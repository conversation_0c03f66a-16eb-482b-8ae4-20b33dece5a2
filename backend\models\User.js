const mongoose = require('mongoose');

/**
 * User Schema for the leaderboard system
 * Each user has a name and total points accumulated
 */
const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'User name is required'],
    trim: true,
    minlength: [2, 'Name must be at least 2 characters long'],
    maxlength: [50, 'Name cannot exceed 50 characters'],
    unique: true
  },
  totalPoints: {
    type: Number,
    default: 0,
    min: [0, 'Total points cannot be negative']
  },
  rank: {
    type: Number,
    default: 0
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index for efficient sorting by points
userSchema.index({ totalPoints: -1 });
userSchema.index({ name: 1 });

// Pre-save middleware to update the updatedAt field
userSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Static method to calculate and update all user rankings
userSchema.statics.updateRankings = async function() {
  try {
    // Get all users sorted by total points (descending)
    const users = await this.find({}).sort({ totalPoints: -1, createdAt: 1 });
    
    // Update ranks
    const bulkOps = users.map((user, index) => ({
      updateOne: {
        filter: { _id: user._id },
        update: { rank: index + 1 }
      }
    }));

    if (bulkOps.length > 0) {
      await this.bulkWrite(bulkOps);
    }

    return users;
  } catch (error) {
    throw new Error(`Error updating rankings: ${error.message}`);
  }
};

// Instance method to add points to user
userSchema.methods.addPoints = async function(points) {
  this.totalPoints += points;
  await this.save();
  return this;
};

const User = mongoose.model('User', userSchema);

module.exports = User;
