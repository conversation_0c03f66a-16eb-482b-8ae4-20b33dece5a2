const User = require('../models/User');

/**
 * Seed initial users if database is empty
 * Creates 10 default users as specified in requirements
 */
const seedUsers = async () => {
  try {
    // Check if users already exist
    const existingUsers = await User.countDocuments();
    
    if (existingUsers > 0) {
      console.log(`Database already has ${existingUsers} users. Skipping seed.`);
      return;
    }

    // Default users as specified in requirements
    const defaultUsers = [
      { name: '<PERSON><PERSON>' },
      { name: '<PERSON>' },
      { name: '<PERSON><PERSON>' },
      { name: '<PERSON><PERSON>' },
      { name: '<PERSON><PERSON>' },
      { name: '<PERSON><PERSON><PERSON>' },
      { name: '<PERSON><PERSON><PERSON>' },
      { name: '<PERSON>' },
      { name: '<PERSON><PERSON><PERSON>' },
      { name: '<PERSON><PERSON>' }
    ];

    // Create users
    const createdUsers = await User.insertMany(defaultUsers);
    
    // Update rankings
    await User.updateRankings();
    
    console.log(`✅ Successfully seeded ${createdUsers.length} users:`);
    createdUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.name} (ID: ${user._id})`);
    });

  } catch (error) {
    console.error('❌ Error seeding users:', error.message);
    throw error;
  }
};

module.exports = { seedUsers };
