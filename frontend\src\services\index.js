// Export all services from a single entry point
export { default as api } from './api';
export { userService } from './userService';
export { leaderboardService } from './leaderboardService';
export { claimHistoryService } from './claimHistoryService';
export { socketService } from './socketService';

// Import services for re-export
import { userService } from './userService';
import { leaderboardService } from './leaderboardService';
import { claimHistoryService } from './claimHistoryService';
import { socketService } from './socketService';

// Re-export for convenience
export {
  userService as users,
  leaderboardService as leaderboard,
  claimHistoryService as claimHistory,
  socketService as socket
};
