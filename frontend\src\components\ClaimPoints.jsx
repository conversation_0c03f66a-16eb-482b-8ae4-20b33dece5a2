import React, { useState } from 'react';
import { toast } from 'react-toastify';
import { leaderboardService } from '../services';

/**
 * ClaimPoints Component
 * Handles claiming random points for selected user
 */
const ClaimPoints = ({ selectedUser, onPointsClaimed }) => {
  const [claiming, setClaiming] = useState(false);
  const [lastClaim, setLastClaim] = useState(null);

  /**
   * Handle claiming points for the selected user
   */
  const handleClaimPoints = async () => {
    if (!selectedUser) {
      toast.error('Please select a user first');
      return;
    }

    try {
      setClaiming(true);
      
      // Call the API to claim points
      const response = await leaderboardService.claimPoints(selectedUser._id);
      
      if (response.success) {
        const { pointsAwarded, newTotal, previousTotal, leaderboard } = response.data;
        
        // Store last claim info
        const claimInfo = {
          user: selectedUser.name,
          pointsAwarded,
          previousTotal,
          newTotal,
          timestamp: new Date().toISOString()
        };
        setLastClaim(claimInfo);
        
        // Show success message
        toast.success(
          `🎉 ${selectedUser.name} claimed ${pointsAwarded} points! Total: ${newTotal}`,
          { autoClose: 4000 }
        );
        
        // Notify parent component
        if (onPointsClaimed) {
          onPointsClaimed({
            user: selectedUser,
            pointsAwarded,
            previousTotal,
            newTotal,
            leaderboard
          });
        }
      }
    } catch (error) {
      console.error('Error claiming points:', error);
      toast.error(error.message || 'Failed to claim points');
    } finally {
      setClaiming(false);
    }
  };

  /**
   * Get random encouragement message
   */
  const getEncouragementMessage = () => {
    const messages = [
      "Feeling lucky? 🍀",
      "Ready to score big? 🎯",
      "Time to claim your fortune! 💰",
      "Let's see what you get! 🎲",
      "Fortune favors the bold! ⚡",
      "Your moment of glory awaits! 🌟",
      "Roll the dice! 🎰",
      "Make your move! 🚀"
    ];
    return messages[Math.floor(Math.random() * messages.length)];
  };

  return (
    <div className="claim-points-container">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-xl font-semibold mb-4 text-gray-800">
          🎯 Claim Points
        </h3>
        
        {/* Claim Button Section */}
        <div className="text-center mb-6">
          {selectedUser ? (
            <div className="space-y-4">
              <p className="text-gray-600 mb-4">
                {getEncouragementMessage()}
              </p>
              
              <button
                onClick={handleClaimPoints}
                disabled={claiming}
                className="claim-button"
              >
                {claiming ? (
                  <>
                    <div className="loading-spinner"></div>
                    Claiming...
                  </>
                ) : (
                  <>
                    🎲 Claim Random Points
                  </>
                )}
              </button>
              
              <p className="text-sm text-gray-500">
                Claim 1-10 random points for <strong>{selectedUser.name}</strong>
              </p>
            </div>
          ) : (
            <div className="py-8">
              <p className="text-gray-500 mb-4">
                👆 Please select a user above to claim points
              </p>
              <button
                disabled
                className="claim-button opacity-50 cursor-not-allowed"
              >
                🎲 Claim Random Points
              </button>
            </div>
          )}
        </div>

        {/* Last Claim Info */}
        {lastClaim && (
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-800 mb-3">🎊 Last Claim:</h4>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="font-semibold text-green-800">
                  {lastClaim.user}
                </span>
                <span className="text-green-600 text-sm">
                  {new Date(lastClaim.timestamp).toLocaleTimeString()}
                </span>
              </div>
              
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-green-600 font-medium">Points Claimed</div>
                  <div className="text-2xl font-bold text-green-800">
                    +{lastClaim.pointsAwarded}
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="text-green-600 font-medium">Previous Total</div>
                  <div className="text-lg font-semibold text-green-700">
                    {lastClaim.previousTotal}
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="text-green-600 font-medium">New Total</div>
                  <div className="text-lg font-semibold text-green-700">
                    {lastClaim.newTotal}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* How it Works */}
        <div className="mt-6 border-t pt-4">
          <h4 className="font-medium text-gray-800 mb-3">ℹ️ How it works:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Select a user from the dropdown above</li>
            <li>• Click "Claim Random Points" to award 1-10 points</li>
            <li>• Points are randomly generated and added to user's total</li>
            <li>• Leaderboard updates automatically in real-time</li>
            <li>• All claims are recorded in the history</li>
          </ul>
        </div>

        {/* Stats */}
        {selectedUser && (
          <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">📊 Current Stats:</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-blue-600">Current Points:</span>
                <span className="ml-2 font-semibold text-blue-800">
                  {selectedUser.totalPoints}
                </span>
              </div>
              <div>
                <span className="text-blue-600">Current Rank:</span>
                <span className="ml-2 font-semibold text-blue-800">
                  #{selectedUser.rank || 'N/A'}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClaimPoints;
