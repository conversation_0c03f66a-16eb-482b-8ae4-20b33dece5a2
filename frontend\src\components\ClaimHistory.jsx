import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { claimHistoryService } from '../services';

/**
 * ClaimHistory Component
 * Displays the history of all point claims with pagination
 */
const ClaimHistory = ({ refreshTrigger }) => {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalClaims: 0,
    hasMore: false,
    limit: 10
  });
  const [loadingMore, setLoadingMore] = useState(false);

  useEffect(() => {
    fetchClaimHistory(1);
  }, [refreshTrigger]);

  /**
   * Fetch claim history from API
   */
  const fetchClaimHistory = async (page = 1, append = false) => {
    try {
      if (!append) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const response = await claimHistoryService.getClaimHistory(page, pagination.limit);
      
      if (response.success) {
        const newHistory = response.data || [];
        
        if (append) {
          setHistory(prevHistory => [...prevHistory, ...newHistory]);
        } else {
          setHistory(newHistory);
        }
        
        setPagination(response.pagination || {});
      }
    } catch (error) {
      console.error('Error fetching claim history:', error);
      toast.error('Failed to load claim history');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  /**
   * Load more history items
   */
  const loadMore = () => {
    if (pagination.hasMore && !loadingMore) {
      fetchClaimHistory(pagination.currentPage + 1, true);
    }
  };

  /**
   * Format date for display
   */
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString()
    };
  };

  /**
   * Get points color based on amount
   */
  const getPointsColor = (points) => {
    if (points >= 8) return 'text-green-600';
    if (points >= 5) return 'text-blue-600';
    if (points >= 3) return 'text-yellow-600';
    return 'text-gray-600';
  };

  /**
   * Get points emoji based on amount
   */
  const getPointsEmoji = (points) => {
    if (points >= 8) return '🔥';
    if (points >= 5) return '⭐';
    if (points >= 3) return '👍';
    return '🎯';
  };

  if (loading) {
    return (
      <div className="claim-history-container">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-4">📜 Claim History</h3>
          <div className="flex items-center justify-center py-8">
            <div className="loading-spinner"></div>
            <span className="ml-2 text-gray-600">Loading history...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="claim-history-container">
      <div className="bg-white rounded-lg shadow-md p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-800">
            📜 Claim History
          </h3>
          <div className="text-sm text-gray-500">
            {pagination.totalClaims} total claims
          </div>
        </div>

        {/* History List */}
        {history.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No claim history found</p>
            <p className="text-sm text-gray-400 mt-2">Start claiming points to see history!</p>
          </div>
        ) : (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {history.map((claim, index) => {
              const { date, time } = formatDate(claim.claimedAt);
              
              return (
                <div
                  key={claim._id || index}
                  className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors fade-in"
                  style={{ animationDelay: `${index * 0.05}s` }}
                >
                  <div className="flex items-center justify-between">
                    {/* User and Points */}
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">
                          {getPointsEmoji(claim.pointsAwarded)}
                        </span>
                        <span className="font-semibold text-gray-800">
                          {claim.userName}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <span className="text-gray-500">claimed</span>
                        <span className={`font-bold text-lg ${getPointsColor(claim.pointsAwarded)}`}>
                          +{claim.pointsAwarded}
                        </span>
                        <span className="text-gray-500">points</span>
                      </div>
                    </div>

                    {/* Date and Time */}
                    <div className="text-right text-sm text-gray-500">
                      <div>{date}</div>
                      <div>{time}</div>
                    </div>
                  </div>

                  {/* Points Change */}
                  <div className="mt-2 flex items-center gap-4 text-sm text-gray-600">
                    <span>
                      {claim.previousTotal} → {claim.newTotal} points
                    </span>
                    <span className="text-gray-400">
                      (+{claim.newTotal - claim.previousTotal})
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Load More Button */}
        {pagination.hasMore && (
          <div className="mt-4 text-center">
            <button
              onClick={loadMore}
              disabled={loadingMore}
              className="btn btn-outline"
            >
              {loadingMore ? (
                <>
                  <div className="loading-spinner"></div>
                  Loading...
                </>
              ) : (
                'Load More'
              )}
            </button>
          </div>
        )}

        {/* Pagination Info */}
        {history.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>
                Showing {history.length} of {pagination.totalClaims} claims
              </span>
              <span>
                Page {pagination.currentPage} of {pagination.totalPages}
              </span>
            </div>
          </div>
        )}

        {/* Refresh Button */}
        <button
          onClick={() => fetchClaimHistory(1)}
          className="btn btn-outline btn-sm mt-4 w-full"
          disabled={loading}
        >
          🔄 Refresh History
        </button>
      </div>
    </div>
  );
};

export default ClaimHistory;
