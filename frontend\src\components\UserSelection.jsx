import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { userService } from '../services';

/**
 * UserSelection Component
 * Handles user selection and adding new users
 */
const UserSelection = ({ selectedUser, onUserSelect, onUserAdded }) => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddUser, setShowAddUser] = useState(false);
  const [newUserName, setNewUserName] = useState('');
  const [addingUser, setAddingUser] = useState(false);

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, []);

  /**
   * Fetch all users from the API
   */
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await userService.getAllUsers();
      setUsers(response.data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle user selection change
   */
  const handleUserChange = (event) => {
    const userId = event.target.value;
    const user = users.find(u => u._id === userId);
    onUserSelect(user);
  };

  /**
   * Handle adding a new user
   */
  const handleAddUser = async (event) => {
    event.preventDefault();
    
    if (!newUserName.trim()) {
      toast.error('Please enter a user name');
      return;
    }

    try {
      setAddingUser(true);
      const response = await userService.createUser(newUserName.trim());
      
      // Add the new user to the list
      const newUser = response.data;
      setUsers(prevUsers => [...prevUsers, newUser]);
      
      // Select the new user
      onUserSelect(newUser);
      
      // Reset form
      setNewUserName('');
      setShowAddUser(false);
      
      toast.success(`User "${newUser.name}" added successfully!`);
      
      // Notify parent component
      if (onUserAdded) {
        onUserAdded(newUser);
      }
    } catch (error) {
      console.error('Error adding user:', error);
      toast.error(error.message || 'Failed to add user');
    } finally {
      setAddingUser(false);
    }
  };

  /**
   * Cancel adding user
   */
  const handleCancelAdd = () => {
    setNewUserName('');
    setShowAddUser(false);
  };

  if (loading) {
    return (
      <div className="user-selection-container">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-4">Select User</h3>
          <div className="flex items-center justify-center py-8">
            <div className="loading-spinner"></div>
            <span className="ml-2 text-gray-600">Loading users...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="user-selection-container">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-xl font-semibold mb-4 text-gray-800">
          👤 Select User
        </h3>
        
        {/* User Selection Dropdown */}
        <div className="mb-4">
          <label htmlFor="user-select" className="block text-sm font-medium text-gray-700 mb-2">
            Choose a user to claim points for:
          </label>
          <select
            id="user-select"
            className="form-select"
            value={selectedUser?._id || ''}
            onChange={handleUserChange}
          >
            <option value="">-- Select a user --</option>
            {users.map(user => (
              <option key={user._id} value={user._id}>
                {user.name} ({user.totalPoints} points)
              </option>
            ))}
          </select>
        </div>

        {/* Add New User Section */}
        <div className="border-t pt-4">
          {!showAddUser ? (
            <button
              onClick={() => setShowAddUser(true)}
              className="btn btn-outline w-full"
              type="button"
            >
              ➕ Add New User
            </button>
          ) : (
            <form onSubmit={handleAddUser} className="space-y-4">
              <div>
                <label htmlFor="new-user-name" className="block text-sm font-medium text-gray-700 mb-2">
                  New User Name:
                </label>
                <input
                  type="text"
                  id="new-user-name"
                  className="form-input"
                  value={newUserName}
                  onChange={(e) => setNewUserName(e.target.value)}
                  placeholder="Enter user name"
                  maxLength={50}
                  disabled={addingUser}
                  autoFocus
                />
              </div>
              
              <div className="flex gap-2">
                <button
                  type="submit"
                  className="btn btn-primary flex-1"
                  disabled={addingUser || !newUserName.trim()}
                >
                  {addingUser ? (
                    <>
                      <div className="loading-spinner"></div>
                      Adding...
                    </>
                  ) : (
                    'Add User'
                  )}
                </button>
                <button
                  type="button"
                  onClick={handleCancelAdd}
                  className="btn btn-outline"
                  disabled={addingUser}
                >
                  Cancel
                </button>
              </div>
            </form>
          )}
        </div>

        {/* Selected User Info */}
        {selectedUser && (
          <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-900 mb-2">Selected User:</h4>
            <div className="flex items-center justify-between">
              <span className="text-blue-800 font-semibold">{selectedUser.name}</span>
              <span className="text-blue-600">
                {selectedUser.totalPoints} points (Rank #{selectedUser.rank || 'N/A'})
              </span>
            </div>
          </div>
        )}

        {/* Users Count */}
        <div className="mt-4 text-sm text-gray-500 text-center">
          Total users: {users.length}
        </div>
      </div>
    </div>
  );
};

export default UserSelection;
