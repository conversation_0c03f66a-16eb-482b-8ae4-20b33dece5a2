import React, { useState, useEffect } from 'react'
import { ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import './App.css'
import { UserSelection, ClaimPoints, Leaderboard, ClaimHistory } from './components'

function App() {
  const [selectedUser, setSelectedUser] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  /**
   * Handle user selection
   */
  const handleUserSelect = (user) => {
    setSelectedUser(user);
  };

  /**
   * Handle when a new user is added
   */
  const handleUserAdded = (newUser) => {
    // Trigger refresh of components that need it
    setRefreshTrigger(prev => prev + 1);
  };

  /**
   * Handle when points are claimed
   */
  const handlePointsClaimed = (claimData) => {
    // Update selected user with new points
    if (selectedUser && claimData.user._id === selectedUser._id) {
      setSelectedUser({
        ...selectedUser,
        totalPoints: claimData.newTotal,
        rank: claimData.user.rank
      });
    }

    // Trigger refresh of history
    setRefreshTrigger(prev => prev + 1);
  };

  /**
   * Handle leaderboard updates
   */
  const handleLeaderboardUpdate = (leaderboard) => {
    // Update selected user if their data changed
    if (selectedUser) {
      const updatedUser = leaderboard.find(user => user._id === selectedUser._id);
      if (updatedUser) {
        setSelectedUser(updatedUser);
      }
    }
  };

  return (
    <div className="App">
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="container mx-auto px-4 py-6">
            <h1 className="text-3xl font-bold text-gray-900 text-center">
              🏆 Leaderboard System
            </h1>
            <p className="text-gray-600 text-center mt-2">
              Select a user and claim random points to compete on the leaderboard!
            </p>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8">
          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto mb-8">
            {/* Left Column - User Selection & Claim Points */}
            <div className="space-y-6">
              <UserSelection
                selectedUser={selectedUser}
                onUserSelect={handleUserSelect}
                onUserAdded={handleUserAdded}
              />

              <ClaimPoints
                selectedUser={selectedUser}
                onPointsClaimed={handlePointsClaimed}
              />
            </div>

            {/* Right Column - Leaderboard */}
            <div>
              <Leaderboard
                onLeaderboardUpdate={handleLeaderboardUpdate}
              />
            </div>
          </div>

          {/* Claim History - Full Width */}
          <div className="max-w-4xl mx-auto">
            <ClaimHistory
              refreshTrigger={refreshTrigger}
            />
          </div>
        </main>

        <footer className="bg-white border-t border-gray-200 mt-12">
          <div className="container mx-auto px-4 py-6 text-center text-gray-500">
            <p>&copy; 2024 Leaderboard System. Built with React & Node.js</p>
          </div>
        </footer>
      </div>

      {/* Toast notifications */}
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  )
}

export default App
