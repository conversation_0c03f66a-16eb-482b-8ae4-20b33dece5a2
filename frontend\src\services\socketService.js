import { io } from 'socket.io-client';

/**
 * Socket Service - Handles real-time communication with the server
 */
class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.listeners = new Map();
  }

  /**
   * Connect to the Socket.IO server
   * @param {string} url - Server URL (optional)
   */
  connect(url = import.meta.env.VITE_SOCKET_URL || 'http://localhost:5000') {
    if (this.socket && this.isConnected) {
      console.log('Socket already connected');
      return;
    }

    try {
      this.socket = io(url, {
        transports: ['websocket', 'polling'],
        timeout: 5000,
        forceNew: true,
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionAttempts: 5,
        maxReconnectionAttempts: 5
      });

      this.setupEventListeners();
      console.log('Socket connection initiated');
    } catch (error) {
      console.error('Error connecting to socket:', error);
    }
  }

  /**
   * Set up default socket event listeners
   */
  setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Socket connected:', this.socket.id);
      this.isConnected = true;
      
      // Join the leaderboard room for real-time updates
      this.socket.emit('join-leaderboard');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      this.isConnected = false;
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.isConnected = false;
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log('Socket reconnected after', attemptNumber, 'attempts');
      this.isConnected = true;
      
      // Rejoin the leaderboard room
      this.socket.emit('join-leaderboard');
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('Socket reconnection error:', error);
    });

    this.socket.on('reconnect_failed', () => {
      console.error('Socket reconnection failed');
    });
  }

  /**
   * Disconnect from the socket server
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.listeners.clear();
      console.log('Socket disconnected');
    }
  }

  /**
   * Listen for leaderboard updates
   * @param {Function} callback - Callback function to handle updates
   */
  onLeaderboardUpdate(callback) {
    if (!this.socket) {
      console.warn('Socket not connected. Call connect() first.');
      return;
    }

    this.socket.on('leaderboard-updated', (data) => {
      console.log('Leaderboard update received:', data);
      callback(data);
    });

    // Store the listener for cleanup
    this.listeners.set('leaderboard-updated', callback);
  }

  /**
   * Remove leaderboard update listener
   */
  offLeaderboardUpdate() {
    if (this.socket && this.listeners.has('leaderboard-updated')) {
      this.socket.off('leaderboard-updated');
      this.listeners.delete('leaderboard-updated');
    }
  }

  /**
   * Listen for any custom event
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  on(event, callback) {
    if (!this.socket) {
      console.warn('Socket not connected. Call connect() first.');
      return;
    }

    this.socket.on(event, callback);
    this.listeners.set(event, callback);
  }

  /**
   * Remove listener for a custom event
   * @param {string} event - Event name
   */
  off(event) {
    if (this.socket && this.listeners.has(event)) {
      this.socket.off(event);
      this.listeners.delete(event);
    }
  }

  /**
   * Emit an event to the server
   * @param {string} event - Event name
   * @param {*} data - Data to send
   */
  emit(event, data) {
    if (!this.socket) {
      console.warn('Socket not connected. Call connect() first.');
      return;
    }

    this.socket.emit(event, data);
  }

  /**
   * Join the leaderboard room
   */
  joinLeaderboard() {
    if (this.socket && this.isConnected) {
      this.socket.emit('join-leaderboard');
    }
  }

  /**
   * Get connection status
   * @returns {boolean} Connection status
   */
  getConnectionStatus() {
    return this.isConnected;
  }

  /**
   * Get socket ID
   * @returns {string|null} Socket ID
   */
  getSocketId() {
    return this.socket ? this.socket.id : null;
  }
}

// Create and export a singleton instance
export const socketService = new SocketService();
export default socketService;
