import api from './api';

/**
 * Leaderboard Service - Handles all leaderboard-related API calls
 */
export const leaderboardService = {
  /**
   * Get the current leaderboard
   * @returns {Promise<Object>} Response with leaderboard data
   */
  async getLeaderboard() {
    try {
      const response = await api.get('/leaderboard');
      return response;
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
      throw error;
    }
  },

  /**
   * Claim points for a user
   * @param {string} userId - The user ID to claim points for
   * @returns {Promise<Object>} Response with claim result and updated leaderboard
   */
  async claimPoints(userId) {
    try {
      const response = await api.post('/leaderboard/claim-points', { userId });
      return response;
    } catch (error) {
      console.error('Error claiming points:', error);
      throw error;
    }
  },

  /**
   * Get leaderboard statistics
   * @returns {Promise<Object>} Response with leaderboard stats
   */
  async getLeaderboardStats() {
    try {
      const response = await api.get('/leaderboard/stats');
      return response;
    } catch (error) {
      console.error('Error fetching leaderboard stats:', error);
      throw error;
    }
  }
};

export default leaderboardService;
