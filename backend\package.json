{"name": "leaderboard-backend", "version": "1.0.0", "description": "Backend API for leaderboard system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["leaderboard", "nodejs", "express", "mongodb"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "socket.io": "^4.7.4", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}}