import api from './api';

/**
 * Claim History Service - Handles all claim history-related API calls
 */
export const claimHistoryService = {
  /**
   * Get claim history with pagination
   * @param {number} page - Page number (default: 1)
   * @param {number} limit - Items per page (default: 20)
   * @returns {Promise<Object>} Response with claim history data
   */
  async getClaimHistory(page = 1, limit = 20) {
    try {
      const response = await api.get('/claim-history', {
        params: { page, limit }
      });
      return response;
    } catch (error) {
      console.error('Error fetching claim history:', error);
      throw error;
    }
  },

  /**
   * Get claim history for a specific user
   * @param {string} userId - The user ID
   * @param {number} limit - Number of records to fetch (default: 20)
   * @returns {Promise<Object>} Response with user's claim history
   */
  async getUserClaimHistory(userId, limit = 20) {
    try {
      const response = await api.get(`/claim-history/user/${userId}`, {
        params: { limit }
      });
      return response;
    } catch (error) {
      console.error('Error fetching user claim history:', error);
      throw error;
    }
  },

  /**
   * Get claim history statistics
   * @returns {Promise<Object>} Response with claim history stats
   */
  async getClaimHistoryStats() {
    try {
      const response = await api.get('/claim-history/stats');
      return response;
    } catch (error) {
      console.error('Error fetching claim history stats:', error);
      throw error;
    }
  }
};

export default claimHistoryService;
