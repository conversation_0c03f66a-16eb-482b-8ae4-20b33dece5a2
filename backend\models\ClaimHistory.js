const mongoose = require('mongoose');

/**
 * ClaimHistory Schema to track all point claims
 * Records every time a user claims points with timestamp and details
 */
const claimHistorySchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  userName: {
    type: String,
    required: [true, 'User name is required'],
    trim: true
  },
  pointsAwarded: {
    type: Number,
    required: [true, 'Points awarded is required'],
    min: [1, 'Points awarded must be at least 1'],
    max: [10, 'Points awarded cannot exceed 10']
  },
  previousTotal: {
    type: Number,
    required: [true, 'Previous total points is required'],
    min: [0, 'Previous total cannot be negative']
  },
  newTotal: {
    type: Number,
    required: [true, 'New total points is required'],
    min: [0, 'New total cannot be negative']
  },
  claimedAt: {
    type: Date,
    default: Date.now,
    required: true
  },
  ipAddress: {
    type: String,
    default: 'unknown'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient querying
claimHistorySchema.index({ userId: 1, claimedAt: -1 });
claimHistorySchema.index({ claimedAt: -1 });
claimHistorySchema.index({ userName: 1 });

// Virtual for formatted claim date
claimHistorySchema.virtual('formattedDate').get(function() {
  return this.claimedAt.toLocaleString();
});

// Static method to get recent claims with pagination
claimHistorySchema.statics.getRecentClaims = async function(limit = 50, skip = 0) {
  try {
    const claims = await this.find({})
      .populate('userId', 'name')
      .sort({ claimedAt: -1 })
      .limit(limit)
      .skip(skip)
      .lean();

    const total = await this.countDocuments();

    return {
      claims,
      total,
      hasMore: (skip + limit) < total
    };
  } catch (error) {
    throw new Error(`Error fetching recent claims: ${error.message}`);
  }
};

// Static method to get user's claim history
claimHistorySchema.statics.getUserHistory = async function(userId, limit = 20) {
  try {
    return await this.find({ userId })
      .sort({ claimedAt: -1 })
      .limit(limit)
      .lean();
  } catch (error) {
    throw new Error(`Error fetching user history: ${error.message}`);
  }
};

// Static method to get claim statistics
claimHistorySchema.statics.getClaimStats = async function() {
  try {
    const stats = await this.aggregate([
      {
        $group: {
          _id: null,
          totalClaims: { $sum: 1 },
          totalPointsAwarded: { $sum: '$pointsAwarded' },
          averagePoints: { $avg: '$pointsAwarded' },
          maxPoints: { $max: '$pointsAwarded' },
          minPoints: { $min: '$pointsAwarded' }
        }
      }
    ]);

    return stats[0] || {
      totalClaims: 0,
      totalPointsAwarded: 0,
      averagePoints: 0,
      maxPoints: 0,
      minPoints: 0
    };
  } catch (error) {
    throw new Error(`Error fetching claim statistics: ${error.message}`);
  }
};

const ClaimHistory = mongoose.model('ClaimHistory', claimHistorySchema);

module.exports = ClaimHistory;
