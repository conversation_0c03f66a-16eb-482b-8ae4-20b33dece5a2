import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { leaderboardService, socketService } from '../services';

/**
 * Leaderboard Component
 * Displays user rankings with real-time updates
 */
const Leaderboard = ({ onLeaderboardUpdate }) => {
  const [leaderboard, setLeaderboard] = useState([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Fetch initial leaderboard data
    fetchLeaderboard();
    
    // Set up socket connection for real-time updates
    setupSocketConnection();
    
    // Cleanup on unmount
    return () => {
      socketService.offLeaderboardUpdate();
    };
  }, []);

  /**
   * Fetch leaderboard data from API
   */
  const fetchLeaderboard = async () => {
    try {
      setLoading(true);
      const response = await leaderboardService.getLeaderboard();
      
      if (response.success) {
        setLeaderboard(response.data || []);
        setLastUpdated(response.lastUpdated || new Date().toISOString());
        
        // Notify parent component
        if (onLeaderboardUpdate) {
          onLeaderboardUpdate(response.data || []);
        }
      }
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
      toast.error('Failed to load leaderboard');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Set up socket connection for real-time updates
   */
  const setupSocketConnection = () => {
    // Connect to socket
    socketService.connect();
    
    // Listen for connection status
    setIsConnected(socketService.getConnectionStatus());
    
    // Listen for leaderboard updates
    socketService.onLeaderboardUpdate((data) => {
      console.log('Real-time leaderboard update received:', data);
      
      if (data.leaderboard) {
        setLeaderboard(data.leaderboard);
        setLastUpdated(new Date().toISOString());
        
        // Show notification for the claim
        if (data.lastClaim) {
          toast.info(
            `${data.lastClaim.user} just claimed ${data.lastClaim.pointsAwarded} points!`,
            { autoClose: 3000 }
          );
        }
        
        // Notify parent component
        if (onLeaderboardUpdate) {
          onLeaderboardUpdate(data.leaderboard);
        }
      }
    });
  };

  /**
   * Get rank badge class based on position
   */
  const getRankBadgeClass = (rank) => {
    switch (rank) {
      case 1:
        return 'rank-badge rank-1';
      case 2:
        return 'rank-badge rank-2';
      case 3:
        return 'rank-badge rank-3';
      default:
        return 'rank-badge rank-other';
    }
  };

  /**
   * Get rank emoji
   */
  const getRankEmoji = (rank) => {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return '🏅';
    }
  };

  /**
   * Format last updated time
   */
  const formatLastUpdated = () => {
    if (!lastUpdated) return '';
    
    const date = new Date(lastUpdated);
    return date.toLocaleString();
  };

  if (loading) {
    return (
      <div className="leaderboard-container">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-4">🏆 Leaderboard</h3>
          <div className="flex items-center justify-center py-8">
            <div className="loading-spinner"></div>
            <span className="ml-2 text-gray-600">Loading leaderboard...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="leaderboard-container">
      <div className="bg-white rounded-lg shadow-md p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-800">
            🏆 Leaderboard
          </h3>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-xs text-gray-500">
              {isConnected ? 'Live' : 'Offline'}
            </span>
          </div>
        </div>

        {/* Leaderboard List */}
        {leaderboard.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No users found</p>
            <p className="text-sm text-gray-400 mt-2">Add some users to get started!</p>
          </div>
        ) : (
          <div className="space-y-3">
            {leaderboard.map((user, index) => (
              <div
                key={user._id}
                className={`user-card flex items-center justify-between p-4 ${
                  index < 3 ? 'border-l-4 border-l-yellow-400' : ''
                } fade-in`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-center gap-4">
                  {/* Rank Badge */}
                  <div className={getRankBadgeClass(user.rank || index + 1)}>
                    {user.rank || index + 1}
                  </div>
                  
                  {/* User Info */}
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-gray-800">
                        {user.name}
                      </span>
                      {(user.rank || index + 1) <= 3 && (
                        <span className="text-lg">
                          {getRankEmoji(user.rank || index + 1)}
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-500">
                      Rank #{user.rank || index + 1}
                    </div>
                  </div>
                </div>

                {/* Points */}
                <div className="text-right">
                  <div className="points-display">
                    {user.totalPoints}
                  </div>
                  <div className="text-sm text-gray-500">
                    points
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Footer */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span>
              Total users: {leaderboard.length}
            </span>
            {lastUpdated && (
              <span>
                Last updated: {formatLastUpdated()}
              </span>
            )}
          </div>
          
          {/* Refresh Button */}
          <button
            onClick={fetchLeaderboard}
            className="btn btn-outline btn-sm mt-2 w-full"
            disabled={loading}
          >
            🔄 Refresh Leaderboard
          </button>
        </div>
      </div>
    </div>
  );
};

export default Leaderboard;
