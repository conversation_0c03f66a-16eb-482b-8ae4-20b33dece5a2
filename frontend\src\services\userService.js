import api from './api';

/**
 * User Service - Handles all user-related API calls
 */
export const userService = {
  /**
   * Get all users
   * @returns {Promise<Object>} Response with users array
   */
  async getAllUsers() {
    try {
      const response = await api.get('/users');
      return response;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  },

  /**
   * Get a single user by ID
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} Response with user data
   */
  async getUserById(userId) {
    try {
      const response = await api.get(`/users/${userId}`);
      return response;
    } catch (error) {
      console.error('Error fetching user:', error);
      throw error;
    }
  },

  /**
   * Create a new user
   * @param {string} name - The user name
   * @returns {Promise<Object>} Response with created user data
   */
  async createUser(name) {
    try {
      const response = await api.post('/users', { name });
      return response;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  },

  /**
   * Update a user
   * @param {string} userId - The user ID
   * @param {string} name - The new user name
   * @returns {Promise<Object>} Response with updated user data
   */
  async updateUser(userId, name) {
    try {
      const response = await api.put(`/users/${userId}`, { name });
      return response;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  },

  /**
   * Delete a user
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} Response confirming deletion
   */
  async deleteUser(userId) {
    try {
      const response = await api.delete(`/users/${userId}`);
      return response;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }
};

export default userService;
