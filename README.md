# Leaderboard System

A real-time leaderboard system built with Node.js backend and React frontend.

## Features

- User selection and management
- Random point claiming (1-10 points)
- Real-time leaderboard updates
- Point claim history tracking
- Responsive and modern UI

## Project Structure

```
├── backend/          # Node.js Express API
│   ├── models/       # Database models
│   ├── routes/       # API routes
│   ├── middleware/   # Custom middleware
│   └── server.js     # Main server file
├── frontend/         # React application
│   ├── src/
│   │   ├── components/
│   │   ├── services/
│   │   └── App.jsx
│   └── public/
└── README.md
```

## Setup Instructions

### Backend Setup

1. Navigate to backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Make sure MongoDB is running locally or update the MONGODB_URI in .env

4. Start the development server:
   ```bash
   npm run dev
   ```

### Frontend Setup

1. Navigate to frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

## API Endpoints

- `GET /api/users` - Get all users
- `POST /api/users` - Create a new user
- `GET /api/leaderboard` - Get leaderboard rankings
- `POST /api/claim-points` - Claim random points for a user
- `GET /api/claim-history` - Get claim history

## Technologies Used

### Backend
- Node.js
- Express.js
- MongoDB with Mongoose
- Socket.IO for real-time updates
- CORS, Helmet for security

### Frontend
- React 18
- Vite
- Axios for API calls
- Socket.IO client
- React Toastify for notifications

## Development

The application supports real-time updates using Socket.IO. When points are claimed, all connected clients receive immediate leaderboard updates.
