const express = require('express');
const router = express.Router();
const User = require('../models/User');
const asyncHandler = require('../middleware/asyncHandler');

/**
 * @route   GET /api/users
 * @desc    Get all users
 * @access  Public
 */
router.get('/', asyncHandler(async (req, res) => {
  const users = await User.find({}).sort({ totalPoints: -1, createdAt: 1 });
  
  res.status(200).json({
    success: true,
    count: users.length,
    data: users
  });
}));

/**
 * @route   GET /api/users/:id
 * @desc    Get single user by ID
 * @access  Public
 */
router.get('/:id', asyncHandler(async (req, res) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  res.status(200).json({
    success: true,
    data: user
  });
}));

/**
 * @route   POST /api/users
 * @desc    Create a new user
 * @access  Public
 */
router.post('/', asyncHandler(async (req, res) => {
  const { name } = req.body;

  // Validation
  if (!name || name.trim().length === 0) {
    return res.status(400).json({
      success: false,
      error: 'User name is required'
    });
  }

  // Check if user already exists
  const existingUser = await User.findOne({ 
    name: { $regex: new RegExp(`^${name.trim()}$`, 'i') } 
  });

  if (existingUser) {
    return res.status(400).json({
      success: false,
      error: 'User with this name already exists'
    });
  }

  // Create user
  const user = await User.create({ name: name.trim() });
  
  // Update rankings for all users
  await User.updateRankings();

  res.status(201).json({
    success: true,
    data: user,
    message: 'User created successfully'
  });
}));

/**
 * @route   PUT /api/users/:id
 * @desc    Update user
 * @access  Public
 */
router.put('/:id', asyncHandler(async (req, res) => {
  const { name } = req.body;

  let user = await User.findById(req.params.id);

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Check if new name already exists (excluding current user)
  if (name && name.trim() !== user.name) {
    const existingUser = await User.findOne({ 
      name: { $regex: new RegExp(`^${name.trim()}$`, 'i') },
      _id: { $ne: user._id }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this name already exists'
      });
    }
  }

  user = await User.findByIdAndUpdate(
    req.params.id,
    { name: name.trim() },
    { new: true, runValidators: true }
  );

  res.status(200).json({
    success: true,
    data: user,
    message: 'User updated successfully'
  });
}));

/**
 * @route   DELETE /api/users/:id
 * @desc    Delete user
 * @access  Public
 */
router.delete('/:id', asyncHandler(async (req, res) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  await User.findByIdAndDelete(req.params.id);
  
  // Update rankings for remaining users
  await User.updateRankings();

  res.status(200).json({
    success: true,
    message: 'User deleted successfully'
  });
}));

module.exports = router;
