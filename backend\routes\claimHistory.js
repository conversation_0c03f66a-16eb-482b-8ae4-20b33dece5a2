const express = require('express');
const router = express.Router();
const ClaimHistory = require('../models/ClaimHistory');
const asyncHandler = require('../middleware/asyncHandler');

/**
 * @route   GET /api/claim-history
 * @desc    Get claim history with pagination
 * @access  Public
 */
router.get('/', asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;

  const result = await ClaimHistory.getRecentClaims(limit, skip);

  res.status(200).json({
    success: true,
    data: result.claims,
    pagination: {
      currentPage: page,
      totalClaims: result.total,
      totalPages: Math.ceil(result.total / limit),
      hasMore: result.hasMore,
      limit
    }
  });
}));

/**
 * @route   GET /api/claim-history/user/:userId
 * @desc    Get claim history for specific user
 * @access  Public
 */
router.get('/user/:userId', asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const limit = parseInt(req.query.limit) || 20;

  const userHistory = await ClaimHistory.getUserHistory(userId, limit);

  res.status(200).json({
    success: true,
    count: userHistory.length,
    data: userHistory
  });
}));

/**
 * @route   GET /api/claim-history/stats
 * @desc    Get claim history statistics
 * @access  Public
 */
router.get('/stats', asyncHandler(async (req, res) => {
  const stats = await ClaimHistory.getClaimStats();

  // Get claims by day for the last 7 days
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

  const dailyStats = await ClaimHistory.aggregate([
    {
      $match: {
        claimedAt: { $gte: sevenDaysAgo }
      }
    },
    {
      $group: {
        _id: {
          $dateToString: {
            format: "%Y-%m-%d",
            date: "$claimedAt"
          }
        },
        count: { $sum: 1 },
        totalPoints: { $sum: "$pointsAwarded" }
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);

  // Get most active users
  const topClaimers = await ClaimHistory.aggregate([
    {
      $group: {
        _id: "$userId",
        userName: { $first: "$userName" },
        claimCount: { $sum: 1 },
        totalPointsClaimed: { $sum: "$pointsAwarded" }
      }
    },
    {
      $sort: { claimCount: -1 }
    },
    {
      $limit: 5
    }
  ]);

  res.status(200).json({
    success: true,
    data: {
      overall: stats,
      dailyStats,
      topClaimers
    }
  });
}));

/**
 * @route   DELETE /api/claim-history/:id
 * @desc    Delete a specific claim record (admin function)
 * @access  Public
 */
router.delete('/:id', asyncHandler(async (req, res) => {
  const claim = await ClaimHistory.findById(req.params.id);

  if (!claim) {
    return res.status(404).json({
      success: false,
      error: 'Claim record not found'
    });
  }

  await ClaimHistory.findByIdAndDelete(req.params.id);

  res.status(200).json({
    success: true,
    message: 'Claim record deleted successfully'
  });
}));

module.exports = router;
