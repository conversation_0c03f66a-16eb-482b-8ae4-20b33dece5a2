const express = require('express');
const router = express.Router();
const User = require('../models/User');
const ClaimHistory = require('../models/ClaimHistory');
const asyncHandler = require('../middleware/asyncHandler');

/**
 * @route   GET /api/leaderboard
 * @desc    Get leaderboard with user rankings
 * @access  Public
 */
router.get('/', asyncHandler(async (req, res) => {
  // Update rankings first
  await User.updateRankings();
  
  // Get all users sorted by rank
  const users = await User.find({}).sort({ rank: 1 });

  res.status(200).json({
    success: true,
    count: users.length,
    data: users,
    lastUpdated: new Date().toISOString()
  });
}));

/**
 * @route   POST /api/leaderboard/claim-points
 * @desc    Claim random points for a user
 * @access  Public
 */
router.post('/claim-points', asyncHandler(async (req, res) => {
  const { userId } = req.body;

  // Validation
  if (!userId) {
    return res.status(400).json({
      success: false,
      error: 'User ID is required'
    });
  }

  // Find user
  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'User not found'
    });
  }

  // Generate random points (1-10)
  const pointsAwarded = Math.floor(Math.random() * 10) + 1;
  const previousTotal = user.totalPoints;

  // Add points to user
  await user.addPoints(pointsAwarded);

  // Create claim history record
  const claimRecord = await ClaimHistory.create({
    userId: user._id,
    userName: user.name,
    pointsAwarded,
    previousTotal,
    newTotal: user.totalPoints,
    ipAddress: req.ip || req.connection.remoteAddress || 'unknown'
  });

  // Update all user rankings
  await User.updateRankings();

  // Get updated leaderboard
  const updatedUsers = await User.find({}).sort({ rank: 1 });

  // Emit real-time update to all connected clients
  const io = req.app.get('io');
  if (io) {
    io.to('leaderboard').emit('leaderboard-updated', {
      leaderboard: updatedUsers,
      lastClaim: {
        user: user.name,
        pointsAwarded,
        newTotal: user.totalPoints,
        timestamp: new Date().toISOString()
      }
    });
  }

  res.status(200).json({
    success: true,
    data: {
      user: user,
      pointsAwarded,
      previousTotal,
      newTotal: user.totalPoints,
      claimId: claimRecord._id,
      leaderboard: updatedUsers
    },
    message: `${user.name} claimed ${pointsAwarded} points!`
  });
}));

/**
 * @route   GET /api/leaderboard/stats
 * @desc    Get leaderboard statistics
 * @access  Public
 */
router.get('/stats', asyncHandler(async (req, res) => {
  const totalUsers = await User.countDocuments();
  const claimStats = await ClaimHistory.getClaimStats();
  
  // Get top performer
  const topUser = await User.findOne({}).sort({ totalPoints: -1 });
  
  // Get recent activity (last 10 claims)
  const recentActivity = await ClaimHistory.getRecentClaims(10);

  res.status(200).json({
    success: true,
    data: {
      totalUsers,
      topUser,
      claimStats,
      recentActivity: recentActivity.claims
    }
  });
}));

module.exports = router;
